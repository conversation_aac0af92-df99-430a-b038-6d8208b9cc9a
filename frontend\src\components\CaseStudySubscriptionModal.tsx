import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import AnimatedRocket from '@/components/AnimatedRocket';

import {
  BookOpen,
  Lock,
  Check,
  Zap,
  Star
} from 'lucide-react';
import caseStudySubscriptionService from '@/services/caseStudySubscriptionService';
import { toast } from 'sonner';

interface CaseStudySubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CaseStudySubscriptionModal: React.FC<CaseStudySubscriptionModalProps> = ({ isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);

  const caseStudyFeatures = [
    {
      icon: <BookOpen className="h-6 w-6 text-white" />,
      title: "The Startup Secrets Library",
      benefits: [
        "Case Study Breakdowns: The exact playbooks 500+ unicorn startups used to scale from $0 to $100M",
        "Behind-the-Scenes Stories: Raw failure stories and breakthrough moments from founders who've been there",
        "Revenue & Marketing Playbooks: Step-by-step systems that generated $2.3B+ in member revenue and landed first 1,000 customers"
      ]
    },
    {
      icon: <Zap className="h-6 w-6 text-white" />,
      title: "Elite Founder Network + Mentorship",
      benefits: [
        "1-on-1 Mentorship Calls: Monthly sessions with StartupStory mentors who've built and sold companies.",
        "Virtual Meetups: Network with founders who've collectively raised and built real business",
        "Strategy & Launch Feedback: Brutally honest feedback on your business ideas from founders who've been there"
      ]
    }
  ];

  const handleSubscribe = async () => {
    setIsLoading(true);
    try {
      await caseStudySubscriptionService.redirectToCheckout();
    } catch (error: any) {
      toast.error(error.message || 'Failed to start subscription process');
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-[95vw] max-h-[95vh] overflow-y-auto p-4 sm:p-6">
        <DialogHeader>
          <DialogTitle className="text-xl sm:text-2xl font-bold text-center flex flex-col sm:flex-row items-center justify-center gap-2">
            <BookOpen className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
            <span className="text-center">Unlock Startup Stories Premium</span>
          </DialogTitle>
        </DialogHeader>

        {/* Pricing Card */}
        <Card className="border-2 border-blue-400 mb-4 sm:mb-6 relative">
          <div className="absolute -top-2 sm:-top-3 left-1/2 transform -translate-x-1/2">
            <Badge className="bg-blue-500 text-white px-2 sm:px-3 py-1 text-xs sm:text-sm">
              <Star className="h-2 w-2 sm:h-3 sm:w-3 mr-1" />
              Limited time offers
            </Badge>
          </div>
          <CardContent className="p-4 sm:p-6 text-center">
            <div className="text-2xl sm:text-3xl font-bold mb-2">
              $300<span className="text-base sm:text-lg text-gray-500">/year</span>
            </div>

            <Button
              size="lg"
              className="w-full bg-blue-500 hover:bg-blue-600 text-sm sm:text-base py-3 sm:py-4"
              onClick={handleSubscribe}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  <span className="text-sm sm:text-base">Processing...</span>
                </div>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Subscribe to Access Unlimited Case Studies</span>
                  <span className="sm:hidden">Subscribe to Premium</span>
                </>
              )}
            </Button>
            <p className="text-gray-600 mb-1 text-sm sm:text-base mt-2">
              Join 1,000+ founders already learning from premium case studies
            </p>
          </CardContent>
        </Card>

        {/* Features Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6">
          {caseStudyFeatures.map((feature, index) => {
            const backgroundClasses = [
              "border-2 border-green-200 bg-green-50",   // First card - green
              "border-2 border-orange-200 bg-orange-50"  // Second card - orange
            ];

            const iconBackgroundClasses = [
              "bg-green-600", // First card - green
              "bg-orange-600"  // Second card - orange
            ];

            return (
              <Card key={index} className={backgroundClasses[index]}>
                <CardContent className="p-3 sm:p-4">
                  <div className="flex items-start sm:items-center mb-3 sm:mb-4">
                    <div className={`w-8 h-8 sm:w-10 sm:h-10 ${iconBackgroundClasses[index]} rounded-lg flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0`}>
                      <div className="scale-75 sm:scale-100">
                        {feature.icon}
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <h3 className="font-semibold text-xs sm:text-sm leading-tight">{feature.title}</h3>
                    </div>
                  </div>
                  <ul className="space-y-1.5 sm:space-y-2">
                    {feature.benefits.map((benefit, benefitIndex) => {
                      const [heading, ...descriptionParts] = benefit.split(':');
                      const description = descriptionParts.join(':').trim();

                      return (
                        <li key={benefitIndex} className="flex items-start text-xs text-gray-700">
                          <Check className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-green-500 mr-1.5 sm:mr-2 flex-shrink-0 mt-0.5" />
                          <span className="leading-tight">
                            <strong>{heading}:</strong> {description}
                          </span>
                        </li>
                      );
                    })}
                  </ul>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Hero Content */}
        <div className="text-center mb-6 sm:mb-8 p-4 sm:p-6 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg">
          <h2 className="text-xl sm:text-2xl md:text-4xl font-bold mb-3 sm:mb-4 leading-tight">
            Imagine building a business that <br className="hidden sm:block" />
            <span className="sm:block">that </span><span className="text-yellow-300">Earns $10K a month</span>
          </h2>
          <p className="text-sm sm:text-lg md:text-xl mb-3 sm:mb-4 max-w-3xl mx-auto text-white/90 leading-relaxed">
            Work for yourself, set your own hours, and choose where you want to be. There's never been a better moment to start something of your own.
          </p>
          <div className="flex flex-col gap-3 sm:gap-4 justify-center items-center mb-4 sm:mb-5">
            <Button
              size="lg"
              onClick={handleSubscribe}
              disabled={isLoading}
              className="bg-yellow-400 text-black hover:bg-yellow-300 text-sm sm:text-lg px-6 sm:px-8 py-4 sm:py-6 flex items-center gap-2 w-full sm:w-auto"
            >
              <AnimatedRocket />
              <span className="hidden sm:inline">Start Building Today</span>
              <span className="sm:hidden">Start Building</span>
            </Button>
          </div>
          <p className="text-white/70 text-xs sm:text-base leading-relaxed">
            Access detailed case studies, and proven strategies from founders who've built successful startups from scratch.
          </p>
        </div>

        {/* Value Proposition */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 mb-4 sm:mb-6">
          <CardContent className="p-4 sm:p-6">
            <div className="text-center">
              <h3 className="text-base sm:text-lg font-bold text-blue-900 mb-2">
                Learn from $1M+ ARR Founders
              </h3>
              <p className="text-blue-800 text-xs sm:text-sm mb-3 sm:mb-4 leading-relaxed">
                Get the exact strategies, mistakes, and insights that helped founders build successful companies
              </p>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 text-center">
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-blue-900">500+</div>
                  <div className="text-xs text-blue-700 leading-tight">Premium Case Studies</div>
                </div>
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-blue-900">$500M+</div>
                  <div className="text-xs text-blue-700 leading-tight">Combined Revenue</div>
                </div>
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-blue-900">1000+</div>
                  <div className="text-xs text-blue-700 leading-tight">Growth Strategies</div>
                </div>
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-blue-900">New</div>
                  <div className="text-xs text-blue-700 leading-tight">Studies Monthly</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Guarantee */}
        <div className="text-center text-xs sm:text-sm text-gray-600 space-y-1 sm:space-y-2">
          <div className="flex items-center justify-center gap-1">
            <Lock className="h-3 w-3 flex-shrink-0" />
            <span>Secure checkout via <span className="font-bold">Stripe</span></span>
          </div>
          <p>Cancel subscription anytime</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CaseStudySubscriptionModal; 