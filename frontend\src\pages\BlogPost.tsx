import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import {
  ChevronUp,
  Bookmark,
  Heart,
  MessageSquare,
  Share2,
  Clock,
  Calendar,
  ArrowLeft,
  Twitter,
  Facebook,
  Linkedin,
  Copy,
  Eye,
  Globe,
  Briefcase,
  MapPin,
  FileText,
  Home,
  Lock,
  Crown
} from 'lucide-react';
import Layout from "@/components/Layout";
import UserLogin from "@/components/UserLogin";
import BlogCommentSection from "@/components/BlogCommentSection";
import RelatedBlogs from "@/components/RelatedBlogs";
import SEO from "@/components/SEO";
import { getBlog, toggleBlogLike } from '@/services/blogService';
import favoriteService from '@/services/favoriteService';
import { Blog, Author } from '@/types';
import { useAuth } from '@/lib/AuthContext';
import { useSEO } from "@/hooks/useSEO";
import { useScrollLoginPopup } from "@/hooks/useScrollLoginPopup";
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { Helmet } from 'react-helmet-async';
import SubscriptionModal from "@/components/CaseStudySubscriptionModal";
import { BlogData } from '@/utils/seoUtils';
import { cn } from "@/lib/utils";

const BACKEND_URL = import.meta.env['VITE_BACKEND_URL'] || 'http://localhost:5000/';

// Extend the Blog type to include premium content properties
interface ExtendedBlog extends Blog {
  subscriptionRequired?: boolean;
  isPremiumContent?: boolean;
  hasSubscriptionAccess?: boolean;
  totalWordCount?: number;
  previewWordCount?: number;
}

// Helper function to get complete avatar URL
const getAvatarUrl = (path: string) => {
  if (!path) return '/default-avatar.png';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

const BlogPost = () => {
  const { blogId } = useParams<{ blogId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [blog, setBlog] = useState<ExtendedBlog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const { isAuthenticated, user } = useAuth();
  const { generateSEO } = useSEO();

  // Scroll-based login popup
  const scrollLoginPopup = useScrollLoginPopup({
    threshold: 40, // Show popup at 40% scroll
    sessionKey: 'blog-scroll-login-popup',
    enabled: true,
    compulsory: true // Make popup compulsory - users must login/register
  });

  // Fetch blog data
  useEffect(() => {
    const fetchBlog = async () => {
      if (!blogId) return;

      try {
        setIsLoading(true);
        setError(null);
        const response = await getBlog(blogId);

        if (response.success) {
          setBlog(response.data);
        } else {
          setError('Blog not found');
        }
      } catch (error) {
        console.error('Error fetching blog:', error);
        setError('Failed to load blog post');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlog();
  }, [blogId]);

  // Update like state when blog data or user changes
  useEffect(() => {
    if (blog && isAuthenticated && user) {
      setIsLiked(blog.likedBy?.includes(user.id) || false);
    } else {
      setIsLiked(false);
    }
  }, [blog, isAuthenticated, user]);

  // Check if blog is bookmarked when user is authenticated and blog is loaded
  useEffect(() => {
    const checkBookmarkStatus = async () => {
      if (isAuthenticated && blog) {
        try {
          const response = await favoriteService.isFavorited('blog', blog._id);
          if (response.success && response.data) {
            setIsBookmarked(response.data.isFavorited);
          }
        } catch (error) {
          console.error('Error checking bookmark status:', error);
        }
      }
    };

    checkBookmarkStatus();
  }, [isAuthenticated, blog]);

  // Handle scroll for reading progress and scroll-to-top button
  useEffect(() => {
    const handleScroll = () => {
      const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
      const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const scrolled = (winScroll / height) * 100;

      setReadingProgress(scrolled);
      setScrollToTopVisible(winScroll > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Get author info
  const getAuthorInfo = (author: string | Author) => {
    if (typeof author === 'string') {
      return { name: 'Anonymous', avatar: '/default-avatar.png', bio: '', id: author };
    }
    return {
      name: author.name,
      avatar: getAvatarUrl(author.avatar),
      bio: author.bio || '',
      isVerified: author.isVerified,
      username: author.username,
      id: author._id || author.id
    };
  };

  // Get featured image URL
  const getFeaturedImageUrl = (blog: ExtendedBlog) => {
    if (blog.featuredImage?.path) {
      return blog.featuredImage.path.startsWith('uploads')
        ? BACKEND_URL + blog.featuredImage.path
        : blog.featuredImage.path;
    }
    return null;
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Share functions
  const shareUrl = window.location.href;
  const shareTitle = blog?.title || '';

  const shareOnTwitter = () => {
    window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareTitle)}`, '_blank');
  };

  const shareOnFacebook = () => {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`, '_blank');
  };

  const shareOnLinkedin = () => {
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`, '_blank');
  };

  const copyLink = () => {
    navigator.clipboard.writeText(shareUrl);
    toast({
      title: "Link copied!",
      description: "The blog post link has been copied to your clipboard.",
    });
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const toggleBookmark = async () => {
    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    if (!blog) return;

    try {
      const response = await favoriteService.toggleFavorite('blog', blog._id);

      if (response.success && response.data) {
        setIsBookmarked(response.data.isFavorited);
        toast({
          title: response.data.isFavorited ? "Blog bookmarked" : "Bookmark removed",
          description: response.data.isFavorited ? "Added to your favorites" : "Removed from your favorites",
        });
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      toast({
        title: "Error",
        description: "Failed to update bookmark status. Please try again.",
        variant: "destructive",
      });
    }
  };

  const toggleLike = async () => {
    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    if (!blog) return;

    try {
      const response = await toggleBlogLike(blog._id);

      if (response.success && response.data) {
        setIsLiked(response.data.isLiked);
        // Update the blog state with new like count
        setBlog(prev => prev ? {
          ...prev,
          likes: response.data.likes,
          likedBy: response.data.likedBy
        } : null);

        toast({
          title: response.data.isLiked ? "Blog liked" : "Blog unliked",
          description: response.data.isLiked ? "Added to your liked blogs" : "Removed from your liked blogs",
        });
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      toast({
        title: "Error",
        description: "Failed to update like status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Generate SEO data when blog is loaded
  const blogSEO = blog ? generateSEO.blog({
    _id: blog._id,
    title: blog.title,
    excerpt: blog.excerpt,
    content: blog.content,
    featuredImage: blog.featuredImage ? {
      url: blog.featuredImage.url,
      path: blog.featuredImage.path
    } : undefined,
    author: {
      name: typeof blog.author === 'object' ? blog.author.name : 'Unknown Author',
      username: typeof blog.author === 'object' ? blog.author.username : 'unknown'
    },
    category: {
      name: typeof blog.category === 'object' ? blog.category.name : 'Uncategorized'
    },
    tags: blog.tags || [],
    publishDate: blog.publishDate,
    updatedAt: blog.updatedAt
  } as BlogData) : null;

  if (isLoading) {
    return (
      <Layout>
        {/* SEO Meta Tags */}
        {blogSEO && <SEO {...blogSEO} />}

        {/* Reading Progress Bar */}
        <div
          className="fixed top-0 left-0 z-50 h-1 bg-primary transition-all duration-300"
          style={{ width: `${readingProgress}%` }}
        />

        <div className="max-w-4xl mx-auto px-4 py-16 flex-1">
          <div className="animate-pulse">
            <div className="h-8 bg-muted rounded mb-4"></div>
            <div className="h-4 bg-muted rounded w-3/4 mb-8"></div>
            <div className="h-64 bg-muted rounded mb-8"></div>
            <div className="space-y-4">
              <div className="h-4 bg-muted rounded"></div>
              <div className="h-4 bg-muted rounded w-5/6"></div>
              <div className="h-4 bg-muted rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !blog) {
    return (
      <Layout>
        <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/30 flex items-center justify-center px-4">
          <div className="text-center max-w-lg mx-auto">
            <div className="relative">
              {/* Decorative background elements */}
              <div className="absolute inset-0 -top-20 -bottom-20">
                <div className="absolute top-8 right-12 w-2 h-2 bg-red-300/40 dark:bg-red-600/40 rounded-full animate-pulse"></div>
                <div className="absolute top-28 left-8 w-1 h-1 bg-red-400/60 dark:bg-red-500/60 rounded-full animate-pulse delay-75"></div>
                <div className="absolute bottom-16 right-20 w-1.5 h-1.5 bg-red-200/50 dark:bg-red-700/50 rounded-full animate-pulse delay-150"></div>
              </div>

              {/* Main content card */}
              <div className="relative bg-card/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-xl shadow-black/5 dark:shadow-black/20">
                {/* Large 404 text */}
                <div className="mb-6">
                  <div className="text-8xl font-bold text-transparent bg-gradient-to-br from-red-500 to-red-600 dark:from-red-400 dark:to-red-500 bg-clip-text mb-2">
                    404
                  </div>
                </div>

                {/* Icon container with enhanced styling */}
                <div className="relative mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/20 rounded-full flex items-center justify-center shadow-lg shadow-red-200/50 dark:shadow-red-900/20 ring-1 ring-red-200/30 dark:ring-red-700/30">
                    <FileText className="w-10 h-10 text-red-600 dark:text-red-400" />
                  </div>
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 w-20 h-20 mx-auto mb-4 bg-red-400/20 dark:bg-red-500/20 rounded-full blur-xl"></div>
                </div>

                <h1 className="text-2xl font-bold text-foreground mb-2 tracking-tight">
                  Blog Post Not Found
                </h1>
                <p className="text-muted-foreground mb-8 leading-relaxed">
                  The blog post you're looking for doesn't exist or has been removed.
                  It may have been deleted or the URL might be incorrect.
                </p>

                {/* Enhanced action buttons */}
                <div className="space-y-3">
                  <Button
                    onClick={() => window.history.back()}
                    className="w-full bg-gradient-to-r from-brand-600 to-brand-700 hover:from-brand-700 hover:to-brand-800 text-white font-medium py-3 rounded-xl shadow-lg shadow-brand-600/25 transition-all duration-200 hover:shadow-xl hover:shadow-brand-600/30 hover:-translate-y-0.5"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Go Back
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/blogs'}
                    className="w-full bg-background/50 hover:bg-muted/50 border-border/70 hover:border-border text-foreground font-medium py-3 rounded-xl transition-all duration-200 hover:shadow-md hover:-translate-y-0.5"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Browse All Blogs
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={() => window.location.href = '/'}
                    className="w-full text-muted-foreground hover:text-foreground font-medium py-3 rounded-xl transition-all duration-200 hover:bg-muted/30"
                  >
                    <Home className="w-4 h-4 mr-2" />
                    Back to Home
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  const authorInfo = getAuthorInfo(blog.author);
  const featuredImageUrl = getFeaturedImageUrl(blog);

  return (
    <Layout>
      {/* SEO Meta Tags */}
      {blogSEO && <SEO {...blogSEO} />}

      {/* Reading Progress Bar */}
      <div
        className="fixed top-0 left-0 z-50 h-1 bg-primary transition-all duration-300"
        style={{ width: `${readingProgress}%` }}
      />

      <article className="max-w-4xl mx-auto flex-1">
        {/* Back Button */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="px-4 pt-8 pb-4"
        >
          <Button
            variant="ghost"
            onClick={() => navigate('/blog')}
            className="text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Blog
          </Button>
        </motion.div>

        {/* Header */}
        <motion.header
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="px-4 pb-8"
        >
          <div className="flex items-center gap-2 mb-4">
            <Badge className="bg-brand-600 hover:bg-brand-700 transition-colors text-white px-3 py-1 text-xs sm:text-sm">
              {typeof blog.category === 'object' ? blog.category.name : blog.category}
            </Badge>
            {blog.isPremium && (
              <Badge className="bg-yellow-500 text-black hover:bg-yellow-600 text-xs sm:text-sm">
                <Crown className="h-3 w-3 mr-1" />
                Premium
              </Badge>
            )}
          </div>

          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold leading-tight mb-4 sm:mb-6 text-foreground">
            {blog.title}
          </h1>

          <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed mb-6 sm:mb-8">
            {blog.excerpt}
          </p>

          {/* Author and Meta Info */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between border-y border-border py-4 sm:py-6 mb-6 sm:mb-8 gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
              {/* Author Details */}
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10 sm:h-12 sm:w-12 ring-2 ring-brand-100">
                  <AvatarImage src={authorInfo.avatar} alt={authorInfo.name} />
                  <AvatarFallback>{authorInfo.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <a href={`/author/@${authorInfo.username}`} className="font-semibold text-sm sm:text-base text-foreground hover:text-brand-600 transition-colors">
                      {authorInfo.name}
                    </a>
                    {authorInfo.isVerified && (
                      <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48" className="sm:w-5 sm:h-5">
                        <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                      </svg>
                    )}
                  </div>
                  {typeof blog.author === 'object' && blog.author.roleTitle && (
                    <span className="text-muted-foreground text-xs sm:text-sm mt-0.5">{blog.author.roleTitle}</span>
                  )}
                </div>
              </div>

              {/* Blog Metadata */}
              <div className="flex flex-wrap items-center gap-2 sm:gap-3 text-xs sm:text-sm text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {formatDate(blog.publishDate)}
                </span>
                <span className="w-1 h-1 rounded-full bg-muted-foreground hidden sm:block"></span>
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {blog.readTime} min read
                </span>
                <span className="w-1 h-1 rounded-full bg-muted-foreground hidden sm:block"></span>
                <span className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  {blog.views} views
                </span>
                <span className="w-1 h-1 rounded-full bg-muted-foreground hidden sm:block"></span>
                <span className="flex items-center gap-1">
                  <Heart className="h-3 w-3" />
                  {blog.likes || 0} likes
                </span>
              </div>
            </div>

            {/* Social Actions */}
            <div className="flex items-center space-x-2 justify-start sm:justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleLike}
                className={`${isLiked ? 'text-red-500' : 'text-muted-foreground'} p-2`}
              >
                <Heart className={`h-4 w-4 sm:h-5 sm:w-5 ${isLiked ? 'fill-current' : ''}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleBookmark}
                className={`${isBookmarked ? 'text-blue-500' : 'text-muted-foreground'} p-2`}
              >
                <Bookmark className={`h-4 w-4 sm:h-5 sm:w-5 ${isBookmarked ? 'fill-current' : ''}`} />
              </Button>
              <Button variant="ghost" size="sm" className="text-muted-foreground p-2">
                <Share2 className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>
            </div>
          </div>
        </motion.header>

        {/* Featured Image */}
        {featuredImageUrl && (
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="mb-8 sm:mb-12 px-4"
          >
            <img
              src={featuredImageUrl}
              alt={blog.title}
              className="w-full h-48 sm:h-64 md:h-80 lg:h-96 object-cover rounded-lg shadow-lg"
            />
          </motion.div>
        )}

        {/* Content */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="px-4"
        >
          <div className="relative">
            {/* Blog content */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
              className={
                cn(
                  "bg-card rounded-xl shadow-sm p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 border border-border relative",
                  blog.subscriptionRequired && blog.isPremiumContent ? "min-h-[460px]" : ""
                )
              }
            >
              <div
                className={
                  `prose prose-sm sm:prose-base lg:prose-lg dark:prose-invert max-w-none`
                }
                style={{
                  lineHeight: '1.7',
                  fontSize: window.innerWidth < 640 ? '16px' : '18px'
                }}
                dangerouslySetInnerHTML={{ __html: blog.content || '' }}
              />

              {/* Premium content overlay for blogs without subscription */}
              {blog.isPremiumContent && blog.subscriptionRequired && (
                <div className="relative mt-8 pt-12">
                  {/* Gradient overlay */}
                  <div className="absolute inset-x-0 top-[-100px] h-[100px] bg-gradient-to-b from-transparent to-white dark:to-gray-900" />

                  {/* Lock card */}
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.3 }}
                    className="text-center bg-card p-4 sm:p-6 lg:p-8 rounded-xl shadow-2xl border border-border relative"
                  >
                    <div className="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/50 dark:to-blue-800/50 rounded-full w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                      <Lock size={24} className="text-blue-600 dark:text-blue-400 sm:w-8 sm:h-8" />
                    </div>
                    <h3 className="font-bold text-lg sm:text-xl mb-2 sm:mb-3 text-gray-900 dark:text-white">Unlock Premium Stories, Case Studies & Insider Playbooks</h3>
                    <div className="space-y-3 mb-4 sm:mb-6">
                      <p className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm leading-relaxed">
                        Every scroll you just made? That's another founder getting ahead while you're stuck reading introductions.
                      </p>
                      <p className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm leading-relaxed">
                        500+ founders went from where you are to building $500M+ in value. Their secret? They stopped learning alone and started learning from others who'd already won.
                      </p>
                      <div className="space-y-2 mt-2">
                        <p className="text-gray-800 dark:text-gray-200 text-sm font-semibold">What you're missing:</p>
                        <div className="flex justify-center">
                          <ul className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm space-y-1 list-disc list-inside text-left">
                            <li>The exact playbooks that built $500M+ in startup value</li>
                            <li>Case studies with real numbers, real strategies, real results</li>
                            <li>Battle-tested frameworks from founders who've actually exited</li>
                            <li>A community of entrepreneurs who've been exactly where you are</li>
                          </ul>
                        </div>
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm font-medium mt-2">
                        For just $197/year (less than $4/week), stop learning the expensive way and start learning from those who've already won.
                      </p>
                      <p className="text-gray-800 dark:text-gray-200 text-sm sm:text-base font-bold">
                          Every day you delay is another costly mistake your competitors won't make.
                        </p>
                    </div>
                    <Button
                      onClick={() => {
                        if (!isAuthenticated) {
                          setShowLoginDialog(true);
                        } else {
                          setShowSubscriptionModal(true);
                        }
                      }}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white w-full font-semibold shadow-lg hover:shadow-xl transition-all duration-200 text-sm sm:text-base"
                      size={window.innerWidth < 640 ? "sm" : "lg"}
                    >
                      {isAuthenticated ? 'Subscribe to Continue Reading' : 'Login to Subscribe'}
                    </Button>
                  </motion.div>
                </div>
              )}
            </motion.div>

            {/* Tags */}
            {blog.tags && blog.tags.length > 0 && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="mb-8 sm:mb-12"
              >
                <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-foreground">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {blog.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs sm:text-sm border-border text-muted-foreground">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Share Section */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="border-t border-b border-border py-6 sm:py-8 mb-8 sm:mb-12"
            >
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <h3 className="text-base sm:text-lg font-semibold mb-2 text-foreground">Share this article</h3>
                  <p className="text-sm sm:text-base text-muted-foreground">Help others discover this content</p>
                </div>
                <div className="flex items-center space-x-2 sm:space-x-3">
                  <Button variant="outline" size="sm" onClick={shareOnTwitter} className="border-border hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 p-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="sm:w-[18px] sm:h-[18px]">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                    </svg>
                  </Button>
                  <Button variant="outline" size="sm" onClick={shareOnFacebook} className="border-border hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 p-2">
                    <Facebook className="h-4 w-4 sm:h-[18px] sm:w-[18px]" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={shareOnLinkedin} className="border-border hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-400 p-2">
                    <Linkedin className="h-4 w-4 sm:h-[18px] sm:w-[18px]" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={copyLink} className="border-border hover:bg-muted/50 p-2">
                    <Copy className="h-4 w-4 sm:h-[18px] sm:w-[18px]" />
                  </Button>
                </div>
              </div>
            </motion.div>

            {/* Author Bio */}
            {typeof blog.author === 'object' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.65 }}
                className="mb-6 sm:mb-8 lg:mb-12 bg-card rounded-xl shadow-sm p-3 sm:p-4 lg:p-6 xl:p-8 border border-border"
              >
                <div className="flex flex-col items-center space-y-3 sm:space-y-0 sm:flex-row sm:items-start sm:space-x-4">
                  {/* Avatar - centered on mobile, left-aligned on larger screens */}
                  <Avatar className="h-12 w-12 sm:h-16 sm:w-16 lg:h-20 lg:w-20 ring-2 sm:ring-4 ring-brand-50 flex-shrink-0">
                    <AvatarImage
                      src={getAvatarUrl(blog.author.avatar)}
                      alt={blog.author.name}
                    />
                    <AvatarFallback className="text-sm sm:text-base lg:text-lg">{blog.author.name.charAt(0)}</AvatarFallback>
                  </Avatar>

                  {/* Author info - stacked on mobile, side-by-side on larger screens */}
                  <div className="flex-1 text-center sm:text-left space-y-2 sm:space-y-3">
                    {/* Name and verification badge */}
                    <div className="flex flex-row items-center justify-center sm:justify-start gap-2">
                      <a
                        href={`/author/@${blog.author.username}`}
                        className="font-bold text-base sm:text-lg lg:text-xl hover:text-brand-600 transition-colors text-foreground"
                      >
                        {blog.author.name}
                      </a>
                      {blog.author.isVerified && (
                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48" className="sm:w-5 sm:h-5 lg:w-6 lg:h-6 flex-shrink-0">
                          <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.920-0.316l-4.706-3.660c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.410-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.410l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                        </svg>
                      )}
                    </div>

                    {/* Location and role - stacked on mobile */}
                    <div className="flex flex-col sm:flex-row sm:items-center justify-center sm:justify-start gap-1 sm:gap-3 text-xs sm:text-sm text-muted-foreground">
                      {blog.author.location && (
                        <div className="flex items-center justify-center sm:justify-start gap-1">
                          <MapPin size={12} className="text-muted-foreground/70 flex-shrink-0" />
                          <span className="truncate">{blog.author.location}</span>
                        </div>
                      )}
                      {blog.author.roleTitle && (
                        <div className="flex items-center justify-center sm:justify-start gap-1">
                          <Briefcase size={12} className="text-muted-foreground/70 flex-shrink-0" />
                          <span className="truncate">{blog.author.roleTitle}</span>
                        </div>
                      )}
                    </div>

                    {/* Social links - centered on mobile, left-aligned on larger screens */}
                    <div className="flex items-center justify-center sm:justify-start gap-2 sm:gap-3 pt-1">
                      {blog.author.websiteUrl && (
                        <a
                          href={blog.author.websiteUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-1 text-muted-foreground hover:text-brand-600 transition-colors rounded-full hover:bg-muted/50"
                          title="Website"
                        >
                          <Globe size={14} className="sm:w-4 sm:h-4" />
                        </a>
                      )}
                      {blog.author.twitterUrl && (
                        <a
                          href={blog.author.twitterUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-1 text-muted-foreground hover:text-brand-600 transition-colors rounded-full hover:bg-muted/50"
                          title="Twitter"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="currentColor" className="sm:w-4 sm:h-4">
                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                          </svg>
                        </a>
                      )}
                      {blog.author.linkedinUrl && (
                        <a
                          href={blog.author.linkedinUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-1 text-muted-foreground hover:text-brand-600 transition-colors rounded-full hover:bg-muted/50"
                          title="LinkedIn"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="currentColor" className="sm:w-4 sm:h-4">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                          </svg>
                        </a>
                      )}
                    </div>
                  </div>
                </div>

                {/* Bio text - separate section for better mobile layout */}
                {blog.author.bio && (
                  <div className="mt-3 sm:mt-4 lg:mt-6 pt-3 sm:pt-4 border-t border-border/50">
                    <p className="text-xs sm:text-sm lg:text-base text-muted-foreground leading-relaxed text-center sm:text-left">
                      {blog.author.bio}
                    </p>
                  </div>
                )}
              </motion.div>
            )}

            {/* Comment Section */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7 }}
              className="mb-8 sm:mb-12"
            >
              <BlogCommentSection blogId={blog._id} />
            </motion.div>

            {/* Related Blogs Section */}
            {authorInfo.id && (
              <RelatedBlogs
                authorId={authorInfo.id}
                authorName={authorInfo.name}
                authorUsername={authorInfo.username || ''}
                currentBlogId={blog._id}
                limit={3}
              />
            )}

            {/* Back to Blog */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.9 }}
              className="text-center mb-8 sm:mb-12"
            >

            </motion.div>
          </div>
        </motion.div>
      </article>

      {/* Scroll to Top Button */}
      {scrollToTopVisible && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-8 right-8 rounded-full shadow-lg z-50"
          onClick={scrollToTop}
        >
          <ChevronUp size={20} />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}

      {/* Login Dialog */}
      <UserLogin
        isOpen={showLoginDialog}
        onClose={() => setShowLoginDialog(false)}
      />

      {/* Subscription Modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
      />

      {/* Scroll-triggered Login Popup */}
      <UserLogin
        isOpen={scrollLoginPopup.isPopupOpen}
        onClose={scrollLoginPopup.closePopup}
        preventScroll={true}
        blurBackground={true}
        compulsory={scrollLoginPopup.isCompulsory}
      />
    </Layout>
  );
};

export default BlogPost;